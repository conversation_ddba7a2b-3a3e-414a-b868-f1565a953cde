/**
 * Receipt Generator
 * Handles receipt generation, HTML creation, and preview functionality
 */

class ReceiptGenerator {
    constructor() {
        this.defaultPaymentMethod = 'cash';
    }

    /**
     * Generate Receipt
     */
    generateReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_generate') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Auto-generate customer info if empty and fill the form fields
        let customerName = document.getElementById('customerName')?.value.trim();
        if (!customerName) {
            customerName = this.generateCustomerName();
            if (document.getElementById('customerName')) {
                document.getElementById('customerName').value = customerName;
            }
        }

        let customerPhone = document.getElementById('customerPhone')?.value.trim();
        if (!customerPhone) {
            customerPhone = this.generateCustomerPhone();
            if (document.getElementById('customerPhone')) {
                document.getElementById('customerPhone').value = customerPhone;
            }
        }

        let customerEmail = document.getElementById('customerEmail')?.value.trim();
        if (!customerEmail) {
            customerEmail = this.generateCustomerEmail();
            if (document.getElementById('customerEmail')) {
                document.getElementById('customerEmail').value = customerEmail;
            }
        }

        // Collect receipt data
        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod, // Use default since form field was removed
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                hidePrice: item.hidePrice || false
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
            // Removed receiptDate - no longer needed
        };

        // Generate receipt HTML and display in preview area
        const receiptHtml = this.generateReceiptHtml(receiptData);
        this.displayReceiptPreview(receiptHtml);

        // Show action buttons
        const previewActions = document.getElementById('previewActions');
        if (previewActions) {
            previewActions.classList.remove('d-none');
        }

        if (typeof UIManager !== 'undefined') {
            UIManager.showMessage('Receipt generated successfully!', 'success');
        }
    }

    /**
     * Generate Receipt HTML
     */
    generateReceiptHtml(data) {
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        return `
            <div class="receipt-container">
                <div class="receipt-inner">
                    ${window.currentLogo && window.currentLogo.src ? `
                    <div class="receipt-logo">
                        <img src="${window.currentLogo.src}" alt="Company Logo">
                    </div>
                    ` : ''}

                    <div class="preview-receipt-header">
                        <div class="receipt-title">${window.LanguageManager ? window.LanguageManager.getText('company_name') : 'KelvinKMS'}</div>
                        <div class="receipt-company-info">
                            ${window.LanguageManager ? window.LanguageManager.getText('company_website') : 'KelvinKMS.com'}<br>
                            ${window.LanguageManager ? window.LanguageManager.getText('company_phone') : '************'}<br>
                            ${window.LanguageManager ? window.LanguageManager.getText('company_email') : '<EMAIL>'}
                        </div>
                    </div>

                    <div class="preview-receipt-info">
                        <div>
                            <div class="receipt-number">
                                Receipt Number: ${data.receiptNumber}
                            </div>
                        </div>
                    </div>

                    <div class="preview-customer-info">
                        <h6>Customer Information</h6>
                        <div class="customer-field">
                            <span class="customer-field-label">Name:</span>
                            <span class="customer-field-value">${data.customer.name || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Phone:</span>
                            <span class="customer-field-value">${data.customer.phone || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Email:</span>
                            <span class="customer-field-value">${data.customer.email || ''}</span>
                        </div>
                        <div class="customer-field">
                            <span class="customer-field-label">Address:</span>
                            <span class="customer-field-value">${data.customer.address || ''}</span>
                        </div>
                    </div>

                    <div class="receipt-items">
                        <div class="items-header">
                            <h6>Items (Total: ${data.items.length} items)</h6>
                        </div>
                        <table class="receipt-table">
                            <thead>
                                <tr>
                                    <th class="text-center receipt-table-header-number-wide">#</th>
                                    <th class="text-left">Item</th>
                                    <th class="text-right">Original Price</th>
                                    <th class="text-right">Discount</th>
                                    <th class="text-right">Final Price</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-right">Total Price</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map((item, index) => {
            const originalPrice = item.originalPrice || item.unitPrice;
            const specialPrice = item.specialPrice || item.unitPrice;
            const discountPercent = item.discountPercent || 0;
            const actualFinalPrice = item.hidePrice ? 0 : specialPrice;

            return `
                                    <tr>
                                        <td class="text-center item-number">${index + 1}</td>
                                        <td class="text-left">
                                            <div class="item-name">${item.name}</div>
                                            ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                                        </td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(originalPrice)}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : (discountPercent > 0 ? discountPercent + '%' : '-')}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(actualFinalPrice)}</td>
                                        <td class="text-center">${item.quantity}</td>
                                        <td class="text-right">${item.hidePrice ? 'N/A' : formatCurrency(item.totalPrice)}</td>
                                    </tr>
                                `;
        }).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="receipt-totals">
                        <table class="totals-table">
                            <tr>
                                <td class="label">Subtotal:</td>
                                <td class="amount">${formatCurrency(data.totals.subtotal)}</td>
                            </tr>
                            ${data.totals.discount > 0 ? `
                            <tr>
                                <td class="label">Discount:</td>
                                <td class="amount">-${formatCurrency(data.totals.discount)}</td>
                            </tr>
                            ` : ''}
                            ${data.totals.tax > 0 ? `
                            <tr>
                                <td class="label">Tax:</td>
                                <td class="amount">${formatCurrency(data.totals.tax)}</td>
                            </tr>
                            ` : ''}
                            <tr class="total-row">
                                <td class="label">Total:</td>
                                <td class="amount">${formatCurrency(data.totals.total)}</td>
                            </tr>
                        </table>
                    </div>

                    <div class="payment-method">
                        <div class="payment-options">
                            ${['Cash', 'Venmo', 'Zelle', 'Square', 'Stripe'].map(pm => {
            return `
                                <div class="payment-option-button">
                                    <div class="payment-checkbox"></div>
                                    <span class="payment-label">${pm}</span>
                                </div>
                                `;
        }).join('')}
                        </div>
                    </div>

                    ${data.notes ? `
                    <div class="receipt-notes">
                        <h6>Notes</h6>
                        <p>${data.notes}</p>
                    </div>
                    ` : ''}

                    <div class="signature-section">
                        <div class="signature-labels-row">
                            <div class="signature-label-item">
                                <span class="signature-label">Seller Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Buyer Signature:</span>
                            </div>
                            <div class="signature-label-item">
                                <span class="signature-label">Signature Date:</span>
                            </div>
                        </div>
                        <div class="signature-lines-area">
                            <div class="signature-line-space"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Display Receipt Preview
     */
    displayReceiptPreview(html) {
        const previewElement = document.getElementById('receiptPreview');
        if (previewElement) {
            previewElement.innerHTML = html;
            previewElement.classList.add('has-content');
        }
    }

    /**
     * Update Receipt Preview
     */
    updateReceiptPreview() {
        console.log('updateReceiptPreview called');

        // This function can be called to refresh the preview
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        console.log('Receipt items for preview:', receiptItems);

        if (receiptItems.length > 0) {
            // Get current customer info without auto-generation
            const customerName = document.getElementById('customerName')?.value.trim() || '';
            const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
            const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

            // Collect receipt data without auto-generation
            const receiptData = {
                customer: {
                    name: customerName,
                    phone: customerPhone,
                    email: customerEmail,
                    address: document.getElementById('customerAddress')?.value.trim() || ''
                },
                paymentMethod: this.defaultPaymentMethod,
                items: receiptItems.map(item => ({
                    name: item.name,
                    category: item.category,
                    description: item.description,
                    quantity: item.quantity,
                    unitPrice: item.unitPrice,
                    totalPrice: item.totalPrice,
                    originalPrice: item.originalPrice || 0,
                    specialPrice: item.specialPrice || item.unitPrice,
                    discountPercent: item.discountPercent || 0,
                    hidePrice: item.hidePrice || false
                })),
                totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
                notes: document.getElementById('notes')?.value.trim() || '',
                receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
            };

            console.log('Receipt data for preview:', receiptData);

            // Generate receipt HTML and display in preview area
            const receiptHtml = this.generateReceiptHtml(receiptData);
            this.displayReceiptPreview(receiptHtml);

            console.log('Receipt preview updated successfully');
        } else {
            // Clear preview if no items
            const previewElement = document.getElementById('receiptPreview');
            if (previewElement) {
                previewElement.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <p>Receipt preview will be shown here with beautiful certificate border</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Calculate Totals (fallback method)
     */
    calculateTotals(items) {
        const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);
        const discountAmount = parseFloat(document.getElementById('discountAmount')?.value) || 0;
        const taxRate = parseFloat(document.getElementById('taxRate')?.value) || 0;

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const taxAmount = discountedSubtotal * (taxRate / 100);
        const total = discountedSubtotal + taxAmount;

        return {
            subtotal: subtotal,
            discount: discountAmount,
            tax: taxAmount,
            total: total
        };
    }

    /**
     * Generate Customer Name
     */
    generateCustomerName() {
        const firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Robert', 'Emily', 'James', 'Jessica'];
        const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];

        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

        return `${firstName} ${lastName}`;
    }

    /**
     * Generate Customer Phone
     */
    generateCustomerPhone() {
        const areaCode = Math.floor(Math.random() * 900) + 100;
        const exchange = Math.floor(Math.random() * 900) + 100;
        const number = Math.floor(Math.random() * 9000) + 1000;

        return `(${areaCode}) ${exchange}-${number}`;
    }

    /**
     * Generate Customer Email
     */
    generateCustomerEmail() {
        const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        const domain = domains[Math.floor(Math.random() * domains.length)];
        const username = 'customer' + Math.floor(Math.random() * 10000);

        return `${username}@${domain}`;
    }

    /**
     * Generate Receipt HTML for Print with Pagination
     */
    generateReceiptHtmlForPrint(data) {
        const formatCurrency = (amount) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 0,
                maximumFractionDigits: 0
            }).format(amount);
        };

        // Enhanced pagination logic to ensure all content is displayed
        const itemsPerPage = 10; // Further reduced to ensure signature section fits
        const pages = [];

        // Split items into pages
        for (let i = 0; i < data.items.length; i += itemsPerPage) {
            pages.push(data.items.slice(i, i + itemsPerPage));
        }

        // If no items, create one empty page
        if (pages.length === 0) {
            pages.push([]);
        }

        // Check if we need an extra page for totals/signatures
        // If last page has more than 6 items, create a separate summary page
        const needsSummaryPage = pages.length > 1 && pages[pages.length - 1].length > 6;
        if (needsSummaryPage) {
            pages.push([]); // Add empty page for summary
        }

        let pagesHtml = '';

        pages.forEach((pageItems, pageIndex) => {
            const isFirstPage = pageIndex === 0;
            const isLastPage = pageIndex === pages.length - 1;
            const isSummaryPage = needsSummaryPage && isLastPage && pageItems.length === 0;

            pagesHtml += `
                <div class="receipt-container" style="page-break-after: ${isLastPage ? 'auto' : 'always'};">
                    <div class="receipt-inner">
                        ${isFirstPage && window.currentLogo && window.currentLogo.src ? `
                        <div class="receipt-logo">
                            <img src="${window.currentLogo.src}" alt="Company Logo">
                        </div>
                        ` : ''}

                        <div class="preview-receipt-header">
                            <div class="receipt-title">${window.LanguageManager ? window.LanguageManager.getText('company_name') : 'KelvinKMS'}</div>
                            <div class="receipt-company-info">
                                ${window.LanguageManager ? window.LanguageManager.getText('company_website') : 'KelvinKMS.com'}<br>
                                ${window.LanguageManager ? window.LanguageManager.getText('company_phone') : '************'}<br>
                                ${window.LanguageManager ? window.LanguageManager.getText('company_email') : '<EMAIL>'}
                            </div>
                        </div>

                        ${isFirstPage ? `
                        <div class="preview-receipt-info">
                            <div>
                                <div class="receipt-number">
                                    Receipt Number: ${data.receiptNumber}
                                </div>
                            </div>
                        </div>

                        <div class="preview-customer-info">
                            <h6>Customer Information</h6>
                            <div class="customer-field">
                                <span class="customer-field-label">Name:</span>
                                <span class="customer-field-value">${data.customer.name || ''}</span>
                            </div>
                            <div class="customer-field">
                                <span class="customer-field-label">Phone:</span>
                                <span class="customer-field-value">${data.customer.phone || ''}</span>
                            </div>
                            <div class="customer-field">
                                <span class="customer-field-label">Email:</span>
                                <span class="customer-field-value">${data.customer.email || ''}</span>
                            </div>
                            <div class="customer-field">
                                <span class="customer-field-label">Address:</span>
                                <span class="customer-field-value">${data.customer.address || ''}</span>
                            </div>
                        </div>
                        ` : !isSummaryPage ? `
                        <div class="print-page-continuation">
                            Items (continued) - Page ${pageIndex + 1}
                        </div>
                        ` : `
                        <div class="print-page-continuation">
                            Receipt Summary - Page ${pageIndex + 1}
                        </div>
                        `}

                        ${!isSummaryPage && pageItems.length > 0 ? `
                        <div class="receipt-items">
                            <table class="receipt-table">
                                <thead>
                                    <tr>
                                        <th class="text-center receipt-table-header-number">#</th>
                                        <th class="text-left">Item</th>
                                        <th class="text-right receipt-table-header-price">Orig. Price</th>
                                        <th class="text-right receipt-table-header-discount">Disc.</th>
                                        <th class="text-right receipt-table-header-price">Final Price</th>
                                        <th class="text-center receipt-table-header-qty">Qty</th>
                                        <th class="text-right receipt-table-header-total">Total Price</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${pageItems.map((item, localIndex) => {
                const globalIndex = pageIndex * itemsPerPage + localIndex + 1;
                const originalPrice = item.originalPrice || item.unitPrice;
                const specialPrice = item.specialPrice || item.unitPrice;
                const discountPercent = item.discountPercent || 0;
                const actualFinalPrice = item.hidePrice ? 0 : specialPrice;

                return `
                                        <tr class="receipt-table-row-compact">
                                            <td class="text-center item-number receipt-table-cell-compact receipt-table-cell-number">${globalIndex}</td>
                                            <td class="text-left receipt-table-cell-compact">
                                                <div class="item-name receipt-table-item-name">${item.name}</div>
                                                ${item.description ? `<div class="item-description receipt-table-item-desc">${item.description}</div>` : ''}
                                            </td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : formatCurrency(originalPrice)}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : (discountPercent > 0 ? discountPercent + '%' : '-')}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-price">${item.hidePrice ? 'N/A' : formatCurrency(actualFinalPrice)}</td>
                                            <td class="text-center receipt-table-cell-compact receipt-table-cell-number">${item.quantity}</td>
                                            <td class="text-right receipt-table-cell-compact receipt-table-cell-number">${item.hidePrice ? 'N/A' : formatCurrency(item.totalPrice)}</td>
                                        </tr>
                                        `;
            }).join('')}
                                </tbody>
                            </table>
                        </div>
                        ` : ''}

                        ${(isLastPage || isSummaryPage) ? `
                        <div class="receipt-totals ${isSummaryPage ? 'print-totals-margin' : 'print-totals-margin-small'}" style="page-break-inside: avoid !important;">
                            <table class="totals-table">
                                <tr>
                                    <td class="label">Subtotal:</td>
                                    <td class="amount">${formatCurrency(data.totals.subtotal)}</td>
                                </tr>
                                ${data.totals.discount > 0 ? `
                                <tr>
                                    <td class="label">Discount:</td>
                                    <td class="amount">-${formatCurrency(data.totals.discount)}</td>
                                </tr>
                                ` : ''}
                                ${data.totals.tax > 0 ? `
                                <tr>
                                    <td class="label">Tax:</td>
                                    <td class="amount">${formatCurrency(data.totals.tax)}</td>
                                </tr>
                                ` : ''}
                                <tr class="total-row">
                                    <td class="label">Total:</td>
                                    <td class="amount">${formatCurrency(data.totals.total)}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="payment-method print-payment-method-margin" style="page-break-inside: avoid !important;">
                            <div class="payment-method-title">
                                Payment Method
                            </div>
                            <div class="payment-options">
                                ${['Cash', 'Venmo', 'Zelle', 'Square', 'Stripe'].map(pm => {
                return `
                                    <div class="payment-option-button">
                                        <div class="payment-checkbox"></div>
                                        <span class="payment-label">${pm}</span>
                                    </div>
                                    `;
            }).join('')}
                            </div>
                        </div>

                        ${data.notes ? `
                        <div class="receipt-notes print-notes-margin" style="page-break-inside: avoid !important;">
                            <h6>Notes</h6>
                            <p>${data.notes}</p>
                        </div>
                        ` : ''}

                        <div class="signature-section print-signature-margin" style="page-break-inside: avoid !important; break-inside: avoid !important; min-height: 180px;">
                            <div class="signature-labels-row" style="page-break-inside: avoid !important;">
                                <div class="signature-label-item">
                                    <span class="signature-label">Seller Signature:</span>
                                </div>
                                <div class="signature-label-item">
                                    <span class="signature-label">Buyer Signature:</span>
                                </div>
                                <div class="signature-label-item">
                                    <span class="signature-label">Signature Date:</span>
                                </div>
                            </div>
                            <div class="signature-lines-area" style="page-break-inside: avoid !important;">
                                <div class="signature-line-space"></div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });

        return pagesHtml;
    }

    /**
     * Print Receipt
     */
    printReceipt() {
        // Get fresh receipt data instead of using cached HTML
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];
        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_print') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Generate fresh receipt data for printing
        const customerName = document.getElementById('customerName')?.value.trim() || '';
        const customerPhone = document.getElementById('customerPhone')?.value.trim() || '';
        const customerEmail = document.getElementById('customerEmail')?.value.trim() || '';

        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod,
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice,
                originalPrice: item.originalPrice || 0,
                specialPrice: item.specialPrice || item.unitPrice,
                discountPercent: item.discountPercent || 0,
                hidePrice: item.hidePrice || false
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || '',
            receiptNumber: document.getElementById('receiptNumber')?.value || 'KMS-UltraVIP-0000001'
        };

        // Generate fresh HTML for printing with pagination
        const receiptContent = this.generateReceiptHtmlForPrint(receiptData);

        const printWindow = window.open('', '_blank');

        const printHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Receipt Print</title>
                ${Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link =>
            `<link href="${link.href}" rel="stylesheet">`
        ).join('')}
                <style>
                    @page {
                        size: letter;
                        margin: 0; /* True borderless printing */
                    }

                    html, body {
                        margin: 0 !important;
                        padding: 0 !important;
                        background: white !important;
                        font-family: 'Courier New', monospace !important;
                        font-size: 12pt !important;
                        line-height: 1.4 !important;
                        width: 100% !important;
                        height: 100% !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    /* Full-bleed elegant border for Letter paper - Perfect borderless */
                    .receipt-container {
                        background: white !important;
                        padding: 0.3in !important; /* Optimal content padding for readability */
                        border-radius: 60px !important; /* Beautiful rounded corners */
                        box-shadow: none !important;
                        border: 4px solid #D4AF37 !important; /* Prominent gold border */
                        box-sizing: border-box !important;
                        width: 8.5in !important; /* Full page width */
                        max-width: 8.5in !important;
                        height: 11in !important; /* Full page height */
                        margin: 0 !important; /* True borderless - no margins */
                        font-family: 'Courier New', monospace !important;
                        line-height: 1.4 !important;
                        font-size: 12pt !important;
                        position: relative !important;
                        background-image: none !important;
                        overflow: hidden !important;
                        /* Enhanced multi-ring border effect */
                        background: 
                            radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
                            radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px),
                            white !important;
                        background-size: 80px 80px, 40px 40px, auto !important;
                        background-position: 0 0, 20px 20px, 0 0 !important;
                    }

                    .receipt-container::before {
                        content: '' !important;
                        position: absolute !important;
                        top: 0.08in !important; /* Minimal inset for perfect borderless look */
                        left: 0.08in !important;
                        right: 0.08in !important;
                        bottom: 0.08in !important;
                        z-index: 1 !important; /* Above background, below content */
                        pointer-events: none !important;
                        /* Primary decorative border ring */
                        border: 3px solid #D4AF37 !important; /* Gold primary ring */
                        border-radius: 54px !important; /* Perfectly aligned inner rounding */
                        /* Enhanced multi-ring border effect for print */
                        box-shadow:
                            0 0 0 4px white,
                            0 0 0 7px #B8860B,
                            0 0 0 10px white,
                            0 0 0 13px #DAA520,
                            inset 0 0 0 3px #F5DEB3 !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        background: linear-gradient(45deg,
                            transparent 0%,
                            rgba(212, 175, 55, 0.05) 25%,
                            transparent 50%,
                            rgba(212, 175, 55, 0.05) 75%,
                            transparent 100%) !important;
                    }

                    /* Enhanced inner decorative ring */
                    .receipt-container::after {
                        content: '' !important;
                        position: absolute !important;
                        top: 0.15in !important;
                        left: 0.15in !important;
                        right: 0.15in !important;
                        bottom: 0.15in !important;
                        z-index: 1 !important;
                        pointer-events: none !important;
                        border: 2px solid #B8860B !important; /* Dark goldenrod inner accent */
                        border-radius: 48px !important; /* Perfectly proportioned inner ring */
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    /* keep ::after visible as inner ring */

                    /* Enhanced Multi-page Support with Consistent Borders */
                    
                    /* Each page gets full borderless treatment */
                    @media print {
                        @page {
                            size: letter;
                            margin: 0 !important; /* True borderless for all pages */
                        }

                        /* Every receipt container (page) gets the same border treatment */
                        .receipt-container {
                            width: 8.5in !important;
                            height: 11in !important;
                            margin: 0 !important;
                            padding: 0.3in !important;
                            border: 4px solid #D4AF37 !important;
                            border-radius: 60px !important;
                            box-sizing: border-box !important;
                            page-break-inside: avoid !important;
                            page-break-after: always !important;
                            position: relative !important;
                            background: 
                                radial-gradient(circle at 20px 20px, #F5DEB3 2px, transparent 2px),
                                radial-gradient(circle at 60px 60px, #F5DEB3 1px, transparent 1px),
                                white !important;
                            background-size: 80px 80px, 40px 40px, auto !important;
                        }

                        /* Ensure every page has the decorative border rings */
                        .receipt-container::before {
                            content: '' !important;
                            position: absolute !important;
                            top: 0.08in !important;
                            left: 0.08in !important;
                            right: 0.08in !important;
                            bottom: 0.08in !important;
                            z-index: 1 !important;
                            border: 3px solid #D4AF37 !important;
                            border-radius: 54px !important;
                            box-shadow:
                                0 0 0 4px white,
                                0 0 0 7px #B8860B,
                                0 0 0 10px white,
                                0 0 0 13px #DAA520,
                                inset 0 0 0 3px #F5DEB3 !important;
                            -webkit-print-color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }

                        .receipt-container::after {
                            content: '' !important;
                            position: absolute !important;
                            top: 0.15in !important;
                            left: 0.15in !important;
                            right: 0.15in !important;
                            bottom: 0.15in !important;
                            z-index: 1 !important;
                            border: 2px solid #B8860B !important;
                            border-radius: 48px !important;
                            -webkit-print-color-adjust: exact !important;
                            print-color-adjust: exact !important;
                        }

                        /* Content positioning above border decorations */
                        .receipt-inner {
                            position: relative !important;
                            z-index: 2 !important;
                        }

                        /* Remove the last page break */
                        .receipt-container:last-child {
                            page-break-after: auto !important;
                        }

                        /* Table pagination with border preservation */
                        .receipt-table {
                            page-break-inside: auto !important;
                            width: 100% !important;
                            border-collapse: collapse !important;
                            font-size: 11px !important;
                            line-height: 1.2 !important;
                        }

                        .receipt-table thead {
                            display: table-header-group !important;
                        }

                        .receipt-table tbody tr {
                            page-break-inside: avoid !important;
                            break-inside: avoid !important;
                        }

                        .receipt-table td,
                        .receipt-table th {
                            padding: 3px 4px !important;
                        }

                        .item-name {
                            font-size: 11px !important;
                            margin-bottom: 2px !important;
                        }

                        .item-description {
                            font-size: 9px !important;
                            color: #666 !important;
                            margin-bottom: 0 !important;
                        }

                        /* Keep important sections together */
                        .receipt-totals,
                        .payment-method,
                        .receipt-notes,
                        .signature-section {
                            page-break-inside: avoid !important;
                        }
                    }

                    /* Base table styles for multi-page support */
                    .receipt-table {
                        page-break-inside: auto !important;
                        width: 100% !important;
                        border-collapse: collapse !important;
                    }

                    .receipt-table thead {
                        display: table-header-group !important;
                    }

                    .receipt-table tbody {
                        display: table-row-group !important;
                    }

                    .receipt-table tbody tr {
                        page-break-inside: avoid !important;
                        break-inside: avoid !important;
                        page-break-after: auto !important;
                    }

                    .receipt-table tr {
                        page-break-inside: avoid !important;
                        page-break-after: auto !important;
                    }

                    .receipt-table thead {
                        display: table-header-group !important;
                    }

                    /* All colors and backgrounds must be preserved */
                    .preview-customer-info {
                        background-color: #8efdeb !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .receipt-table thead tr {
                        background-color: #81c0ff !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .payment-method {
                        background-color: #f8f9fa !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .payment-option-button {
                        border: 2px solid #D4AF37 !important;
                        background: white !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                    }

                    .no-print {
                        display: none !important;
                    }
                </style>
            </head>
            <body>
                ${receiptContent}
                <script>
                    window.onload = function() {
                        setTimeout(function() {
                            window.print();
                            window.close();
                        }, 1500);
                    };
                </script>
            </body>
            </html>
        `;

        printWindow.document.write(printHtml);
        printWindow.document.close();
    }

    /**
     * Save Receipt
     */
    async saveReceipt() {
        const receiptItems = window.ItemManager ? window.ItemManager.getReceiptItems() : [];

        if (receiptItems.length === 0) {
            if (typeof UIManager !== 'undefined') {
                const message = window.LanguageManager ?
                    window.LanguageManager.getText('error_no_items_to_save') :
                    'Please add items to the receipt first';
                UIManager.showMessage(message, 'warning');
            }
            return;
        }

        // Auto-generate customer info if empty and fill the form fields
        let customerName = document.getElementById('customerName')?.value.trim();
        if (!customerName) {
            customerName = this.generateCustomerName();
            if (document.getElementById('customerName')) {
                document.getElementById('customerName').value = customerName;
            }
        }

        let customerPhone = document.getElementById('customerPhone')?.value.trim();
        if (!customerPhone) {
            customerPhone = this.generateCustomerPhone();
            if (document.getElementById('customerPhone')) {
                document.getElementById('customerPhone').value = customerPhone;
            }
        }

        let customerEmail = document.getElementById('customerEmail')?.value.trim();
        if (!customerEmail) {
            customerEmail = this.generateCustomerEmail();
            if (document.getElementById('customerEmail')) {
                document.getElementById('customerEmail').value = customerEmail;
            }
        }

        const receiptData = {
            customer: {
                name: customerName,
                phone: customerPhone,
                email: customerEmail,
                address: document.getElementById('customerAddress')?.value.trim() || ''
            },
            paymentMethod: this.defaultPaymentMethod,
            items: receiptItems.map(item => ({
                name: item.name,
                category: item.category,
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                totalPrice: item.totalPrice
            })),
            totals: window.ItemManager ? window.ItemManager.calculateTotals() : this.calculateTotals(receiptItems),
            notes: document.getElementById('notes')?.value.trim() || ''
        };

        try {
            // First save to database
            const response = await fetch('php/save_receipt.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(receiptData)
            });

            const result = await response.json();

            if (result.success) {
                // Generate and download PDF
                this.generatePDF(receiptData, result.receipt_number);

                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('Receipt saved successfully and PDF generated!', 'success');
                }
            } else {
                if (typeof UIManager !== 'undefined') {
                    UIManager.showMessage('Failed to save receipt: ' + result.message, 'error');
                }
            }
        } catch (error) {
            console.error('Error saving receipt:', error);
            if (typeof UIManager !== 'undefined') {
                UIManager.showMessage('Error occurred while saving receipt', 'error');
            }
        }
    }

    /**
     * Generate PDF Receipt - Using html2canvas for exact styling
     */
    async generatePDF(receiptData, receiptNumber) {
        try {
            // Show loading message
            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('正在生成PDF，請稍候...', 'info');
            } else {
                console.log('正在生成PDF，請稍候...');
            }

            // Get the receipt preview element
            const receiptElement = document.getElementById('receiptPreview');
            if (!receiptElement || !receiptElement.querySelector('.receipt-container')) {
                throw new Error('Receipt preview not found. Please generate receipt first.');
            }

            const receiptContainer = receiptElement.querySelector('.receipt-container');

            // Create a temporary container for PDF generation with proper styling
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'absolute';
            tempContainer.style.left = '-9999px';
            tempContainer.style.top = '0';
            tempContainer.style.width = '794px'; // A4 width in pixels at 96 DPI
            tempContainer.style.minHeight = '1123px'; // A4 height in pixels at 96 DPI
            tempContainer.style.background = 'white';
            tempContainer.style.padding = '40px';
            tempContainer.style.fontFamily = 'Courier New, monospace';
            tempContainer.style.fontSize = '12px';
            tempContainer.style.lineHeight = '1.4';
            tempContainer.style.boxSizing = 'border-box';

            // Clone the receipt content and adjust styles for PDF
            const clonedContent = receiptContainer.cloneNode(true);

            // Adjust styles for better PDF rendering
            clonedContent.style.width = '100%';
            clonedContent.style.maxWidth = 'none';
            clonedContent.style.margin = '0';
            clonedContent.style.padding = '20px';
            clonedContent.style.borderRadius = '15px';
            clonedContent.style.fontSize = '12px';

            tempContainer.appendChild(clonedContent);
            document.body.appendChild(tempContainer);

            // Configure html2canvas options for better quality
            const canvas = await html2canvas(tempContainer, {
                scale: 2, // Higher resolution
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                logging: false,
                removeContainer: false
            });

            // Remove temporary container
            document.body.removeChild(tempContainer);

            // Create PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'pt',
                format: 'letter'
            });

            // Calculate dimensions for proper scaling
            const pdfWidth = 595.28; // A4 width in points
            const pdfHeight = 841.89; // A4 height in points
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;

            // Calculate scale to fit width
            const scale = pdfWidth / canvasWidth;
            const scaledHeight = canvasHeight * scale;

            // Check if content fits on one page
            if (scaledHeight <= pdfHeight) {
                // Single page - fit to width
                const imgData = canvas.toDataURL('image/png', 1.0);
                pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, scaledHeight);
            } else {
                // Multiple pages needed
                const pageHeight = pdfHeight / scale; // Height in canvas pixels per page
                let position = 0;
                let pageNumber = 1;

                while (position < canvasHeight) {
                    // Create canvas for current page
                    const pageCanvas = document.createElement('canvas');
                    const pageCtx = pageCanvas.getContext('2d');
                    const currentPageHeight = Math.min(pageHeight, canvasHeight - position);

                    pageCanvas.width = canvasWidth;
                    pageCanvas.height = currentPageHeight;

                    // Fill with white background
                    pageCtx.fillStyle = '#ffffff';
                    pageCtx.fillRect(0, 0, pageCanvas.width, pageCanvas.height);

                    // Draw portion of original canvas
                    pageCtx.drawImage(
                        canvas,
                        0, position, canvasWidth, currentPageHeight,
                        0, 0, canvasWidth, currentPageHeight
                    );

                    // Add page to PDF
                    if (pageNumber > 1) {
                        pdf.addPage();
                    }

                    const pageImgData = pageCanvas.toDataURL('image/png', 1.0);
                    const scaledPageHeight = currentPageHeight * scale;
                    pdf.addImage(pageImgData, 'PNG', 0, 0, pdfWidth, scaledPageHeight);

                    position += pageHeight;
                    pageNumber++;
                }
            }

            // Save PDF
            const fileName = `Receipt_${receiptNumber || new Date().getTime()}.pdf`;
            pdf.save(fileName);

            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('PDF生成成功！', 'success');
            } else {
                console.log('PDF生成成功！');
            }

        } catch (error) {
            console.error('Error generating PDF:', error);
            if (typeof UIManager !== 'undefined' && UIManager.showMessage) {
                UIManager.showMessage('PDF生成失敗: ' + error.message, 'error');
            } else {
                console.error('PDF生成失敗: ' + error.message);
            }
        }
    }
}

// Create global instance
window.ReceiptGenerator = new ReceiptGenerator();

// Export functions for backward compatibility
window.generateReceipt = () => window.ReceiptGenerator.generateReceipt();
window.generateReceiptHtml = (data) => window.ReceiptGenerator.generateReceiptHtml(data);
window.displayReceiptPreview = (html) => window.ReceiptGenerator.displayReceiptPreview(html);
window.updateReceiptPreview = () => window.ReceiptGenerator.updateReceiptPreview();
window.printReceipt = () => window.ReceiptGenerator.printReceipt();
window.saveReceipt = () => window.ReceiptGenerator.saveReceipt();
